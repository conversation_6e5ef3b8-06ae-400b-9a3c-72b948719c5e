"""
Revenue Simulation Model - Technical Implementation
---------------------------------------------------
This document outlines the architecture and steps to implement a simulation engine that meets:
- Hard revenue and customer count objectives (start and end)
- Targeted customer mix by segment (PLG vs Mid-Market)
- Business realism via flexible churn, acquisition, GMV, plan migrations

The model will use Quadratic Programming (QP) with soft constraints to simulate month-by-month dynamics.
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize

# ---------------------------- CONFIGURATION ---------------------------- #

MONTHS = ["Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
START_CUSTOMERS = 954
END_CUSTOMERS = 372
START_PLG_RATIO = 0.8
END_PLG_RATIO = 0.25

REVENUE_TARGETS = [82600, 91800, 104100, 119700, 134700, 151300, 169600, 181800, 199100, 217300, 241800, 262100]

PLAN_GMV_RANGES = {
    "starter": (200, 400),
    "pro": (400, 800),
    "team": (2000, 4000),
    "enterprise": (4000, 6000)
}

SUB_FEES = {
    "starter": 10,
    "pro": 25,
    "team": 100,
    "enterprise": 200
}

GMV_FEE = {
    "starter": 0.1,
    "pro": 0.1,
    "team": 0.1,
    "enterprise": 0.1
}

CHURN_RANGE = {
    "PLG": (0.02, 0.08),
    "MidMarket": (0.01, 0.04)
}

# ---------------------------- HELPER FUNCTIONS ---------------------------- #

def simulate_gmv(plan):
    low, high = PLAN_GMV_RANGES[plan]
    return np.random.uniform(low, high)

def revenue_per_customer(plan, gmv):
    return SUB_FEES[plan] + GMV_FEE[plan] * gmv

def plan_migration(current_distribution):
    # Placeholder: deterministic or stochastic transitions between plans
    return current_distribution  # For now, assume sticky plans

# ---------------------------- OBJECTIVE FUNCTION ---------------------------- #

def objective(vars, revenue_target, customer_target, penalty=0.5):
    """
    vars: [starter, pro, team, enterprise]
    Computes total revenue and customer count and applies penalty for deviation
    """
    n = vars
    gmv_estimates = [simulate_gmv(plan) for plan in ["starter", "pro", "team", "enterprise"]]
    revenue = sum([revenue_per_customer(p, gmv_estimates[i]) * n[i] for i, p in enumerate(["starter", "pro", "team", "enterprise"])] )
    customers = sum(n)
    return -(revenue - penalty * ((revenue - revenue_target)**2 + (customers - customer_target)**2))

# ---------------------------- OPTIMIZATION ---------------------------- #

def solve_month(month_idx, prev_distribution):
    revenue_target = REVENUE_TARGETS[month_idx]
    total_target = np.linspace(START_CUSTOMERS, END_CUSTOMERS, len(MONTHS))[month_idx]

    bounds = [(0, total_target)] * 4  # 4 plans
    constraints = ({'type': 'eq', 'fun': lambda x: sum(x) - total_target})

    initial_guess = [total_target / 4] * 4

    result = minimize(
        objective,
        initial_guess,
        args=(revenue_target, total_target),
        method="SLSQP",
        bounds=bounds,
        constraints=constraints,
        options={"disp": False}
    )

    return result.x, result.fun

# ---------------------------- MAIN LOOP ---------------------------- #

def run_simulation():
    plan_distributions = []
    for idx, month in enumerate(MONTHS):
        prev = plan_distributions[-1] if plan_distributions else [START_CUSTOMERS * START_PLG_RATIO * 0.5, START_CUSTOMERS * START_PLG_RATIO * 0.5, START_CUSTOMERS * 0.1, START_CUSTOMERS * 0.1]
        dist, _ = solve_month(idx, prev)
        plan_distributions.append(dist)

    df = pd.DataFrame(plan_distributions, columns=["starter", "pro", "team", "enterprise"])
    df["month"] = MONTHS
    df["total_customers"] = df[["starter", "pro", "team", "enterprise"]].sum(axis=1)
    df["estimated_revenue"] = df.apply(
        lambda row: sum([revenue_per_customer(p, simulate_gmv(p)) * row[p] for p in ["starter", "pro", "team", "enterprise"]]), axis=1)
    df["target_revenue"] = REVENUE_TARGETS
    df["revenue_gap"] = df["estimated_revenue"] - df["target_revenue"]
    df["customer_target"] = np.linspace(START_CUSTOMERS, END_CUSTOMERS, len(MONTHS))
    df["customer_gap"] = df["total_customers"] - df["customer_target"]
    return df

if __name__ == "__main__":
    df_result = run_simulation()
    print(df_result)
    df_result.to_csv("final_simulation_output.csv", index=False)
