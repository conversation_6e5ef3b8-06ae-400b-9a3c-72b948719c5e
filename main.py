"""
Revenue Simulation Model - Technical Implementation
---------------------------------------------------
This document outlines the architecture and steps to implement a simulation engine that meets:
- Hard revenue and customer count objectives (start and end)
- Targeted customer mix by segment (PLG vs Mid-Market)
- Business realism via flexible churn, acquisition, GMV, plan migrations

The model will use Quadratic Programming (QP) with soft constraints to simulate month-by-month dynamics.
"""

import numpy as np
import pandas as pd
from scipy.optimize import linprog

# ---------------------------- CONFIGURATION ---------------------------- #

MONTHS = [
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
]
START_CUSTOMERS = 954
END_CUSTOMERS = 372
START_PLG_RATIO = 0.8
END_PLG_RATIO = 0.25

REVENUE_TARGETS = [
    82600,
    91800,
    104100,
    119700,
    134700,
    151300,
    169600,
    181800,
    199100,
    217300,
    241800,
    262100,
]

# PLG ratio for each month (Apr to Mar)
PLG_RATIOS = [0.8, 0.75, 0.7, 0.7, 0.65, 0.6, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25]

# Seasonal GMV multipliers per month (Q4 holiday season boost)
SEASONAL_MULTIPLIERS = [1.0, 1.05, 1.1, 1.15, 1.2, 1.25, 1.4, 1.7, 1.8, 1.3, 1.2, 1.1]

# GMV ranges per plan (allowing for business growth and high-value customers)
PLAN_GMV_RANGES = {
    "starter": (50, 150),  # PLG plan: $1.5-4.5 commission
    "pro": (100, 300),  # PLG plan: $3-9 commission
    "growth": (200, 500),  # PLG plan: $6-15 commission
    "team": (1000, 8000),  # MidMarket plan: $30-240 commission
    "enterprise": (5000, 100000),  # MidMarket plan: $150-3000 commission
}

# Base GMV values (used for optimization)
PLAN_GMV_BASE = {
    "starter": 100,  # PLG plan
    "pro": 200,  # PLG plan
    "growth": 350,  # PLG plan
    "team": 2000,  # MidMarket plan
    "enterprise": 20000,  # MidMarket plan (increased to enable $3000 revenue)
}

# Business growth multipliers over time (customers grow their usage)
BUSINESS_GROWTH_MULTIPLIERS = [
    1.0,
    1.05,
    1.1,
    1.15,
    1.2,
    1.25,
    1.3,
    1.4,
    1.5,
    1.6,
    1.7,
    1.8,
]

SUB_FEES = {
    "starter": 29,  # PLG plan
    "pro": 49,  # PLG plan
    "growth": 99,  # PLG plan
    "team": 199,  # MidMarket plan
    "enterprise": 499,  # MidMarket plan
}

# Commission rate (only applies to MidMarket plans)
COMMISSION_RATE = 0.03

# Plan categories
PLG_PLANS = ["starter", "pro", "growth"]
MIDMARKET_PLANS = ["team", "enterprise"]

# Churn rates by month (Jan to Dec)
# Mid-Market: 4% in Jan declining to 1% in Dec
MIDMARKET_CHURN_RATES = [
    0.04,
    0.035,
    0.03,
    0.025,
    0.02,
    0.018,
    0.016,
    0.014,
    0.012,
    0.01,
    0.01,
    0.01,
]

# PLG: 12-20% every month (we'll use a base rate with some variation)
PLG_CHURN_RATES = [
    0.16,
    0.18,
    0.15,
    0.17,
    0.14,
    0.19,
    0.13,
    0.20,
    0.12,
    0.16,
    0.18,
    0.15,
]

# Q4 acquisition constraints (Oct, Nov, Dec have reduced Mid-Market acquisition)
Q4_MONTHS = [6, 7, 8]  # Oct=6, Nov=7, Dec=8 (0-indexed)
Q4_MIDMARKET_ACQUISITION_REDUCTION = 0.3  # 30% reduction in new Mid-Market customers

# PLG plan distribution (realistic customer behavior)
# Typical PLG distribution: majority on starter/pro, smaller portion on growth
PLG_PLAN_DISTRIBUTION = {
    "starter_ratio": 0.5,  # 50% of PLG customers on starter
    "pro_ratio": 0.35,  # 35% of PLG customers on pro
    "growth_ratio": 0.15,  # 15% of PLG customers on growth
}

# ---------------------------- HELPER FUNCTIONS ---------------------------- #


def get_gmv(plan, seasonal_multiplier=1.0, business_growth_multiplier=1.0):
    """Get GMV for a plan with optional seasonal and business growth multipliers"""
    return PLAN_GMV_BASE[plan] * seasonal_multiplier * business_growth_multiplier


def revenue_per_customer(plan, seasonal_multiplier=1.0, business_growth_multiplier=1.0):
    """Calculate revenue per customer based on plan, seasonality, and business growth"""
    base_fee = SUB_FEES[plan]
    gmv = get_gmv(plan, seasonal_multiplier, business_growth_multiplier)

    # Only MidMarket plans get commission
    if plan in MIDMARKET_PLANS:
        commission = gmv * COMMISSION_RATE
        return base_fee + commission
    return base_fee


def get_churn_rates(month_idx):
    """Get churn rates for PLG and Mid-Market segments for a given month"""
    # Note: month_idx is 0-based, but churn rates are defined for calendar months
    # Apr=0 maps to index 3 in calendar year, Jan=8 maps to index 0, etc.
    calendar_month_idx = (month_idx + 3) % 12  # Apr=3, May=4, ..., Jan=0, Feb=1, Mar=2

    plg_churn = PLG_CHURN_RATES[calendar_month_idx]
    midmarket_churn = MIDMARKET_CHURN_RATES[calendar_month_idx]

    return plg_churn, midmarket_churn


def apply_churn(current_distribution, month_idx):
    """Apply churn to current customer distribution"""
    plg_churn, midmarket_churn = get_churn_rates(month_idx)

    # Apply churn to each plan
    churned_distribution = []
    for i, plan in enumerate(["starter", "pro", "growth", "team", "enterprise"]):
        current_count = current_distribution[i]
        if plan in PLG_PLANS:
            remaining = current_count * (1 - plg_churn)
        else:  # Mid-Market plans
            remaining = current_count * (1 - midmarket_churn)
        churned_distribution.append(int(remaining))

    return churned_distribution


def plan_migration(current_distribution):
    # Placeholder: deterministic or stochastic transitions between plans
    return current_distribution  # For now, assume sticky plans


# ---------------------------- OPTIMIZATION ---------------------------- #


def get_monthly_revenue_vector(month_idx):
    """Get revenue per customer for each plan for a given month"""
    seasonal_multiplier = SEASONAL_MULTIPLIERS[month_idx]
    business_growth_multiplier = BUSINESS_GROWTH_MULTIPLIERS[month_idx]
    revenue_per_plan = []
    for plan in ["starter", "pro", "growth", "team", "enterprise"]:
        revenue = revenue_per_customer(
            plan, seasonal_multiplier, business_growth_multiplier
        )
        revenue_per_plan.append(revenue)
    return np.array(revenue_per_plan)


def solve_month(month_idx, prev_distribution=None):
    """Solve for optimal customer distribution using linear programming with Q4 constraints"""
    revenue_target = REVENUE_TARGETS[month_idx]
    total_customers = int(
        np.linspace(START_CUSTOMERS, END_CUSTOMERS, len(MONTHS))[month_idx]
    )
    plg_ratio = PLG_RATIOS[month_idx]

    # Get revenue per customer for each plan this month
    revenue_per_plan = get_monthly_revenue_vector(month_idx)

    # Q4 Mid-Market acquisition constraints (Oct, Nov, Dec)
    is_q4_month = month_idx in Q4_MONTHS

    # If we have previous distribution, account for existing customers after churn
    if prev_distribution is not None and is_q4_month:
        # In Q4, focus on expansion revenue rather than new Mid-Market acquisition
        # Maintain existing Mid-Market customers but limit new acquisitions
        existing_midmarket = (
            prev_distribution[3] + prev_distribution[4]
        )  # team + enterprise
        max_new_midmarket = int(existing_midmarket * Q4_MIDMARKET_ACQUISITION_REDUCTION)

        # Calculate total Mid-Market customers allowed (existing + limited new)
        max_midmarket_customers = existing_midmarket + max_new_midmarket
        midmarket_ratio = 1 - plg_ratio
        target_midmarket = int(total_customers * midmarket_ratio)

        # Use the more restrictive constraint
        effective_midmarket_limit = min(max_midmarket_customers, target_midmarket)
    else:
        effective_midmarket_limit = total_customers  # No special Q4 constraints

    # Plan margins (reduced bias to allow realistic PLG distribution)
    plan_margins = np.array(
        [5, 10, 20, 300, 600]
    )  # starter, pro, growth, team, enterprise
    margin_weight = -plan_margins  # Negative for maximization

    # Calculate PLG customer targets based on realistic distribution
    plg_customers_total = int(plg_ratio * total_customers)
    starter_target = int(plg_customers_total * PLG_PLAN_DISTRIBUTION["starter_ratio"])
    pro_target = int(plg_customers_total * PLG_PLAN_DISTRIBUTION["pro_ratio"])
    growth_target = plg_customers_total - starter_target - pro_target

    # Constraints:
    # 1. Revenue constraint: sum(revenue_per_plan * customers) = revenue_target
    # 2. Customer constraint: sum(customers) = total_customers
    # 3. PLG constraint: starter + pro + growth = plg_ratio * total_customers
    # 4. PLG distribution constraints (with some flexibility)

    A_eq = [
        revenue_per_plan,  # Revenue constraint
        [1, 1, 1, 1, 1],  # Total customers constraint
        [1, 1, 1, 0, 0],  # PLG customers constraint (starter + pro + growth)
    ]
    b_eq = [revenue_target, total_customers, plg_ratio * total_customers]

    # Bounds: each plan can have 0 to total_customers, with PLG distribution constraints
    bounds = [(0, total_customers) for _ in range(5)]

    # Apply realistic PLG plan distribution bounds (with some flexibility)
    flexibility_factor = 0.3  # Allow 30% deviation from target distribution
    bounds[0] = (
        max(0, int(starter_target * (1 - flexibility_factor))),
        min(total_customers, int(starter_target * (1 + flexibility_factor))),
    )  # starter
    bounds[1] = (
        max(0, int(pro_target * (1 - flexibility_factor))),
        min(total_customers, int(pro_target * (1 + flexibility_factor))),
    )  # pro
    bounds[2] = (
        max(0, int(growth_target * (1 - flexibility_factor))),
        min(total_customers, int(growth_target * (1 + flexibility_factor))),
    )  # growth

    # Apply Q4 Mid-Market acquisition limits
    if is_q4_month and prev_distribution is not None:
        # Limit team + enterprise customers in Q4
        bounds[3] = (0, min(total_customers, effective_midmarket_limit))  # team
        bounds[4] = (0, min(total_customers, effective_midmarket_limit))  # enterprise

    # Solve using linear programming
    result = linprog(margin_weight, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method="highs")

    if result.success:
        plan_counts = np.round(result.x).astype(int)
        # Verify revenue calculation
        actual_revenue = np.dot(plan_counts, revenue_per_plan)
        return plan_counts, actual_revenue

    # Fallback: distribute proportionally if optimization fails
    plg_customers = int(plg_ratio * total_customers)
    midmarket_customers = total_customers - plg_customers
    plan_counts = [
        plg_customers // 3,  # starter
        plg_customers // 3,  # pro
        plg_customers - 2 * (plg_customers // 3),  # growth
        midmarket_customers // 2,  # team
        midmarket_customers - midmarket_customers // 2,  # enterprise
    ]
    actual_revenue = np.dot(plan_counts, revenue_per_plan)
    return plan_counts, actual_revenue


# ---------------------------- MAIN LOOP ---------------------------- #


def run_simulation():
    plan_distributions = []
    actual_revenues = []

    for idx, month in enumerate(MONTHS):
        if idx == 0:
            # First month: start with initial distribution
            prev_distribution = None
        else:
            # Apply churn to previous month's distribution
            prev_distribution = apply_churn(plan_distributions[-1], idx)

        dist, actual_revenue = solve_month(idx, prev_distribution)
        plan_distributions.append(dist)
        actual_revenues.append(actual_revenue)

    df = pd.DataFrame(
        plan_distributions, columns=["starter", "pro", "growth", "team", "enterprise"]
    )
    df["month"] = MONTHS
    df["total_customers"] = df[["starter", "pro", "growth", "team", "enterprise"]].sum(
        axis=1
    )
    df["estimated_revenue"] = actual_revenues
    df["target_revenue"] = REVENUE_TARGETS
    df["revenue_gap"] = df["estimated_revenue"] - df["target_revenue"]
    df["customer_target"] = np.linspace(START_CUSTOMERS, END_CUSTOMERS, len(MONTHS))
    df["customer_gap"] = df["total_customers"] - df["customer_target"]

    # Add PLG ratio information
    df["plg_ratio_target"] = PLG_RATIOS
    df["plg_customers"] = df["starter"] + df["pro"] + df["growth"]
    df["plg_ratio_actual"] = df["plg_customers"] / df["total_customers"]
    df["plg_ratio_gap"] = df["plg_ratio_actual"] - df["plg_ratio_target"]

    # Add Mid-Market customer count
    df["midmarket_customers"] = df["team"] + df["enterprise"]

    # Add customer acquisition and churn tracking by plan
    df["starter_new"] = 0
    df["starter_churned"] = 0
    df["pro_new"] = 0
    df["pro_churned"] = 0
    df["growth_new"] = 0
    df["growth_churned"] = 0
    df["team_new"] = 0
    df["team_churned"] = 0
    df["enterprise_new"] = 0
    df["enterprise_churned"] = 0

    # Add total acquisition and churn tracking
    df["total_new_customers"] = 0
    df["total_customers_churned"] = 0

    # Calculate ARPU for PLG and Mid-Market segments
    df["plg_arpu"] = 0.0
    df["midmarket_arpu"] = 0.0

    for i in range(len(df)):
        month_idx = i
        seasonal_multiplier = SEASONAL_MULTIPLIERS[month_idx]
        business_growth_multiplier = BUSINESS_GROWTH_MULTIPLIERS[month_idx]

        # Calculate PLG ARPU (weighted average across starter, pro, growth)
        plg_revenue = 0
        plg_customers_total = df.iloc[i]["plg_customers"]
        if plg_customers_total > 0:
            for plan in PLG_PLANS:
                plan_customers = df.iloc[i][plan]
                if plan_customers > 0:
                    plan_revenue = revenue_per_customer(
                        plan, seasonal_multiplier, business_growth_multiplier
                    )
                    plg_revenue += plan_customers * plan_revenue
            df.loc[i, "plg_arpu"] = plg_revenue / plg_customers_total

        # Calculate Mid-Market ARPU (weighted average across team, enterprise)
        midmarket_revenue = 0
        midmarket_customers_total = df.iloc[i]["midmarket_customers"]
        if midmarket_customers_total > 0:
            for plan in MIDMARKET_PLANS:
                plan_customers = df.iloc[i][plan]
                if plan_customers > 0:
                    plan_revenue = revenue_per_customer(
                        plan, seasonal_multiplier, business_growth_multiplier
                    )
                    midmarket_revenue += plan_customers * plan_revenue
            df.loc[i, "midmarket_arpu"] = midmarket_revenue / midmarket_customers_total

    # Calculate customer acquisition and churn by plan for each month
    plan_names = ["starter", "pro", "growth", "team", "enterprise"]

    for i in range(len(df)):
        if i == 0:
            # First month: all customers are "acquired" (starting base)
            for j, plan in enumerate(plan_names):
                df.loc[i, f"{plan}_new"] = df.iloc[i][plan]
                df.loc[i, f"{plan}_churned"] = 0

            df.loc[i, "total_new_customers"] = df.iloc[i]["total_customers"]
            df.loc[i, "total_customers_churned"] = 0
        else:
            # Calculate churn and acquisition by plan
            prev_distribution = [df.iloc[i - 1][plan] for plan in plan_names]
            churned_distribution = apply_churn(prev_distribution, i)
            current_distribution = [df.iloc[i][plan] for plan in plan_names]

            total_churned = 0
            total_new = 0

            for j, plan in enumerate(plan_names):
                prev_count = prev_distribution[j]
                remaining_after_churn = churned_distribution[j]
                current_count = current_distribution[j]

                # Calculate churn for this plan
                churned = prev_count - remaining_after_churn

                # Calculate net change (could be negative if customers downgrade)
                net_change = current_count - remaining_after_churn
                new_acquired = max(
                    0, net_change
                )  # Only count positive as new acquisition

                df.loc[i, f"{plan}_churned"] = max(0, churned)
                df.loc[i, f"{plan}_new"] = new_acquired

                total_churned += max(0, churned)
                total_new += new_acquired

            df.loc[i, "total_customers_churned"] = total_churned
            df.loc[i, "total_new_customers"] = total_new

    # Round customer counts to integers
    customer_columns = [
        "starter",
        "pro",
        "growth",
        "team",
        "enterprise",
        "total_customers",
        "plg_customers",
        "midmarket_customers",
        "new_customers_acquired",
        "customers_churned",
    ]
    for col in customer_columns:
        df[col] = df[col].round().astype(int)

    df["customer_target"] = df["customer_target"].round().astype(int)
    df["customer_gap"] = df["customer_gap"].round().astype(int)

    # Round revenue to 2 decimal places
    revenue_columns = ["estimated_revenue", "revenue_gap", "plg_arpu", "midmarket_arpu"]
    for col in revenue_columns:
        df[col] = df[col].round(2)

    # Round ratios to 2 decimal places
    ratio_columns = ["plg_ratio_target", "plg_ratio_actual", "plg_ratio_gap"]
    for col in ratio_columns:
        df[col] = df[col].round(2)

    return df


if __name__ == "__main__":
    df_result = run_simulation()
    print(df_result)
    df_result.to_csv("final_simulation_output.csv", index=False)
