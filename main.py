"""
Revenue Simulation Model - Technical Implementation
---------------------------------------------------
This document outlines the architecture and steps to implement a simulation engine that meets:
- Hard revenue and customer count objectives (start and end)
- Targeted customer mix by segment (PLG vs Mid-Market)
- Business realism via flexible churn, acquisition, GMV, plan migrations

The model will use Quadratic Programming (QP) with soft constraints to simulate month-by-month dynamics.
"""

import numpy as np
import pandas as pd
from scipy.optimize import linprog

# ---------------------------- CONFIGURATION ---------------------------- #

MONTHS = [
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
]
START_CUSTOMERS = 954
END_CUSTOMERS = 372
START_PLG_RATIO = 0.8
END_PLG_RATIO = 0.25

REVENUE_TARGETS = [
    82600,
    91800,
    104100,
    119700,
    134700,
    151300,
    169600,
    181800,
    199100,
    217300,
    241800,
    262100,
]

# PLG ratio for each month (Apr to Mar)
PLG_RATIOS = [0.8, 0.75, 0.7, 0.7, 0.65, 0.6, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25]

# Seasonal GMV multipliers per month
SEASONAL_MULTIPLIERS = [1.0, 1.05, 1.1, 1.15, 1.2, 1.25, 1.3, 1.5, 1.6, 1.3, 1.2, 1.1]

# Fixed GMV values per plan (more realistic)
PLAN_GMV = {
    "starter": 100,  # PLG plan
    "pro": 200,  # PLG plan
    "team": 500,  # MidMarket plan
    "enterprise": 1000,  # MidMarket plan
}

SUB_FEES = {"starter": 10, "pro": 25, "team": 100, "enterprise": 200}

# Commission rate (only applies to MidMarket plans)
COMMISSION_RATE = 0.1

# Plan categories
PLG_PLANS = ["starter", "pro"]
MIDMARKET_PLANS = ["team", "enterprise"]

CHURN_RANGE = {"PLG": (0.02, 0.08), "MidMarket": (0.01, 0.04)}

# ---------------------------- HELPER FUNCTIONS ---------------------------- #


def get_gmv(plan, seasonal_multiplier=1.0):
    """Get GMV for a plan with optional seasonal multiplier"""
    return PLAN_GMV[plan] * seasonal_multiplier


def revenue_per_customer(plan, seasonal_multiplier=1.0):
    """Calculate revenue per customer based on plan and seasonality"""
    base_fee = SUB_FEES[plan]
    gmv = get_gmv(plan, seasonal_multiplier)

    # Only MidMarket plans get commission
    if plan in MIDMARKET_PLANS:
        commission = gmv * COMMISSION_RATE
        return base_fee + commission
    else:
        return base_fee


def plan_migration(current_distribution):
    # Placeholder: deterministic or stochastic transitions between plans
    return current_distribution  # For now, assume sticky plans


# ---------------------------- OPTIMIZATION ---------------------------- #


def get_monthly_revenue_vector(month_idx):
    """Get revenue per customer for each plan for a given month"""
    seasonal_multiplier = SEASONAL_MULTIPLIERS[month_idx]
    revenue_per_plan = []
    for plan in ["starter", "pro", "team", "enterprise"]:
        revenue = revenue_per_customer(plan, seasonal_multiplier)
        revenue_per_plan.append(revenue)
    return np.array(revenue_per_plan)


def solve_month(month_idx, prev_distribution=None):
    """Solve for optimal customer distribution using linear programming"""
    revenue_target = REVENUE_TARGETS[month_idx]
    total_customers = int(
        np.linspace(START_CUSTOMERS, END_CUSTOMERS, len(MONTHS))[month_idx]
    )
    plg_ratio = PLG_RATIOS[month_idx]

    # Get revenue per customer for each plan this month
    revenue_per_plan = get_monthly_revenue_vector(month_idx)

    # Plan margins (used to bias LP objective toward higher-margin plans)
    plan_margins = np.array([10, 25, 150, 300])  # starter, pro, team, enterprise
    margin_weight = -plan_margins  # Negative for maximization

    # Constraints:
    # 1. Revenue constraint: sum(revenue_per_plan * customers) = revenue_target
    # 2. Customer constraint: sum(customers) = total_customers
    # 3. PLG constraint: starter + pro = plg_ratio * total_customers

    A_eq = [
        revenue_per_plan,  # Revenue constraint
        [1, 1, 1, 1],  # Total customers constraint
        [1, 1, 0, 0],  # PLG customers constraint (starter + pro)
    ]
    b_eq = [revenue_target, total_customers, plg_ratio * total_customers]

    # Bounds: each plan can have 0 to total_customers
    bounds = [(0, total_customers) for _ in range(4)]

    # Solve using linear programming
    result = linprog(margin_weight, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method="highs")

    if result.success:
        plan_counts = np.round(result.x).astype(int)
        # Verify revenue calculation
        actual_revenue = np.dot(plan_counts, revenue_per_plan)
        return plan_counts, actual_revenue
    else:
        # Fallback: distribute proportionally if optimization fails
        plg_customers = int(plg_ratio * total_customers)
        midmarket_customers = total_customers - plg_customers
        plan_counts = [
            plg_customers // 2,  # starter
            plg_customers - plg_customers // 2,  # pro
            midmarket_customers // 2,  # team
            midmarket_customers - midmarket_customers // 2,  # enterprise
        ]
        actual_revenue = np.dot(plan_counts, revenue_per_plan)
        return plan_counts, actual_revenue


# ---------------------------- MAIN LOOP ---------------------------- #


def run_simulation():
    plan_distributions = []
    for idx, month in enumerate(MONTHS):
        prev = (
            plan_distributions[-1]
            if plan_distributions
            else [
                START_CUSTOMERS * START_PLG_RATIO * 0.5,
                START_CUSTOMERS * START_PLG_RATIO * 0.5,
                START_CUSTOMERS * 0.1,
                START_CUSTOMERS * 0.1,
            ]
        )
        dist, _ = solve_month(idx, prev)
        plan_distributions.append(dist)

    df = pd.DataFrame(
        plan_distributions, columns=["starter", "pro", "team", "enterprise"]
    )
    df["month"] = MONTHS
    df["total_customers"] = df[["starter", "pro", "team", "enterprise"]].sum(axis=1)
    df["estimated_revenue"] = df.apply(
        lambda row: sum(
            [
                revenue_per_customer(p, 1.0) * row[p]
                for p in ["starter", "pro", "team", "enterprise"]
            ]
        ),
        axis=1,
    )
    df["target_revenue"] = REVENUE_TARGETS
    df["revenue_gap"] = df["estimated_revenue"] - df["target_revenue"]
    df["customer_target"] = np.linspace(START_CUSTOMERS, END_CUSTOMERS, len(MONTHS))
    df["customer_gap"] = df["total_customers"] - df["customer_target"]
    return df


if __name__ == "__main__":
    df_result = run_simulation()
    print(df_result)
    df_result.to_csv("final_simulation_output.csv", index=False)
