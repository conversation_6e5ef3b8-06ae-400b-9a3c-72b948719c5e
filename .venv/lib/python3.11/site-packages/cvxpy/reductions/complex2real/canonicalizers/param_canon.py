"""
Copyright 2013 <PERSON>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

from cvxpy.atoms.affine.imag import imag
from cvxpy.atoms.affine.real import real


def param_canon(expr, real_args, imag_args, real2imag):
    if expr.is_real():
        return expr, None
    elif expr.is_imag():
        return None, imag(expr)
    else:
        return (real(expr), imag(expr))
