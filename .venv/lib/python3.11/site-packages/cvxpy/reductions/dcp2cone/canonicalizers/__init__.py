"""
Copyright 2013 <PERSON>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

from cvxpy.atoms import *
from cvxpy.atoms.affine.index import special_index
from cvxpy.atoms.suppfunc import SuppFuncAtom
from cvxpy.reductions.cone2cone.approximations import (
    von_neumann_entr_canon_dispatch,)
from cvxpy.reductions.dcp2cone.canonicalizers.quantum_rel_entr_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.entr_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.exp_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.geo_mean_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.huber_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.indicator_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.kl_div_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.lambda_max_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.lambda_sum_largest_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.log1p_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.log_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.log_det_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.log_sum_exp_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.logistic_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.matrix_frac_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.normNuc_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.perspective_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.pnorm_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.power_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.quad_form_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.quad_over_lin_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.rel_entr_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.sigma_max_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.suppfunc_canon import (
    suppfunc_canon,)
from cvxpy.reductions.dcp2cone.canonicalizers.tr_inv_canon import *
from cvxpy.reductions.dcp2cone.canonicalizers.xexp_canon import *
from cvxpy.reductions.eliminate_pwl.canonicalizers import (abs_canon,
                                                           cummax_canon,
                                                           cumsum_canon,
                                                           dotsort_canon,
                                                           max_canon,
                                                           maximum_canon,
                                                           min_canon,
                                                           minimum_canon,
                                                           norm1_canon,
                                                           norm_inf_canon,
                                                           sum_largest_canon,)
from cvxpy.reductions.utilities import special_index_canon
from cvxpy.transforms.indicator import indicator

# TODO: remove pwl canonicalize methods, use EliminatePwl reduction instead
CANON_METHODS = {
    cummax : cummax_canon,
    cumsum : cumsum_canon,
    geo_mean : geo_mean_canon,
    lambda_max : lambda_max_canon,
    lambda_sum_largest : lambda_sum_largest_canon,
    log_det : log_det_canon,
    log_sum_exp : log_sum_exp_canon,
    MatrixFrac : matrix_frac_canon,
    max : max_canon,
    min : min_canon,
    norm1 : norm1_canon,
    normNuc : normNuc_canon,
    norm_inf : norm_inf_canon,
    Pnorm : pnorm_canon,
    QuadForm : quad_form_canon,
    quad_over_lin : quad_over_lin_canon,
    sigma_max : sigma_max_canon,
    sum_largest : sum_largest_canon,
    abs : abs_canon,
    entr : entr_canon,
    exp : exp_canon,
    huber : huber_canon,
    kl_div : kl_div_canon,
    log : log_canon,
    log1p : log1p_canon,
    logistic : logistic_canon,
    maximum : maximum_canon,
    minimum : minimum_canon,
    perspective : perspective_canon,
    power : power_canon,
    rel_entr : rel_entr_canon,
    indicator : indicator_canon,
    special_index : special_index_canon,
    SuppFuncAtom : suppfunc_canon,
    xexp : xexp_canon,
    dotsort : dotsort_canon,
    von_neumann_entr : von_neumann_entr_canon_dispatch,
    quantum_rel_entr: quantum_rel_entr_canon,
    tr_inv : tr_inv_canon,
}
