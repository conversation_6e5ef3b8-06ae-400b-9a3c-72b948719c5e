import pandas as pd
import numpy as np


def normalize_probs(prob_dict):
    total = sum(prob_dict.values())
    if total == 0:
        return [1.0 / len(prob_dict)] * len(prob_dict)
    return [v / total for v in prob_dict.values()]


def simulate_customer_revenue_matrix(
    start_customers,
    end_customers,
    months,
    monthly_revenue_targets,
    plg_ratio_start,
    plg_ratio_end,
    plans,
    subscription_fee,
    commission_rate,
    churn_rate=0.03,
    seasonality=None,
    deterministic_topup=True,
    seed=42,
):
    if seasonality is None:
        seasonality = {
            "Jan": 1.0,
            "Feb": 0.95,
            "Mar": 1.05,
            "Apr": 1.1,
            "May": 1.2,
            "Jun": 1.3,
            "Jul": 1.25,
            "Aug": 1.1,
            "Sep": 1.0,
            "Oct": 1.15,
            "Nov": 1.25,
            "Dec": 1.4,
        }

    np.random.seed(seed)
    customer_id_counter = 1
    customers = {}
    journey_log = []

    target_end_counts = np.linspace(start_customers, end_customers, len(months)).astype(
        int
    )
    plg_ratios = np.linspace(plg_ratio_start, plg_ratio_end, len(months))

    for _ in range(start_customers):
        cid = f"C{customer_id_counter:06d}"
        customers[cid] = {
            "active": True,
            "segment": "PLG",
            "plan": np.random.choice(
                list(plans["PLG"].keys()),
                p=normalize_probs(
                    {k: v["percentage"] for k, v in plans["PLG"].items()}
                ),
            ),
        }
        customer_id_counter += 1

    for idx, month in enumerate(months):
        seasonal_multiplier = seasonality[month]
        plg_ratio = plg_ratios[idx]
        target_revenue = monthly_revenue_targets[idx]
        target_customers = target_end_counts[idx]

        active_customers = [cid for cid, data in customers.items() if data["active"]]
        churned_ids = np.random.choice(
            active_customers,
            size=int(len(active_customers) * churn_rate),
            replace=False,
        )
        for cid in churned_ids:
            customers[cid]["active"] = False

        current_active = [cid for cid, data in customers.items() if data["active"]]
        acquisition_needed = max(target_customers - len(current_active), 0)
        for _ in range(acquisition_needed):
            cid = f"C{customer_id_counter:06d}"
            segment = "PLG" if np.random.rand() < plg_ratio else "MidMarket"
            plan = np.random.choice(
                list(plans[segment].keys()),
                p=normalize_probs(
                    {k: v["percentage"] for k, v in plans[segment].items()}
                ),
            )
            customers[cid] = {"active": True, "segment": segment, "plan": plan}
            customer_id_counter += 1

        upgrade_candidates = [
            cid
            for cid in customers
            if customers[cid]["active"] and customers[cid]["segment"] == "PLG"
        ]
        upgrades = np.random.choice(
            upgrade_candidates, size=int(0.05 * len(upgrade_candidates)), replace=False
        )
        for cid in upgrades:
            customers[cid]["segment"] = "MidMarket"
            customers[cid]["plan"] = np.random.choice(
                list(plans["MidMarket"].keys()),
                p=normalize_probs(
                    {k: v["percentage"] for k, v in plans["MidMarket"].items()}
                ),
            )

        monthly_revenue = 0
        month_log = []

        for cid, data in customers.items():
            if not data["active"]:
                continue
            segment, plan = data["segment"], data["plan"]
            base_fee = subscription_fee[segment][plan]
            gmv = plans[segment][plan]["gmv"] * seasonal_multiplier
            revenue = base_fee + (
                commission_rate * gmv if segment == "MidMarket" else 0
            )
            monthly_revenue += revenue
            month_log.append({"Month": month, "Customer_ID": cid, "Revenue": revenue})

        # Precise iterative top-up loop
        while monthly_revenue < target_revenue:
            cid = f"C{customer_id_counter:06d}"

            if deterministic_topup:
                plan = (
                    "enterprise"
                    if (target_revenue - monthly_revenue) > 20000
                    else "team"
                )
            else:
                plan = np.random.choice(
                    list(plans["MidMarket"].keys()),
                    p=normalize_probs(
                        {k: v["percentage"] for k, v in plans["MidMarket"].items()}
                    ),
                )

            gmv = plans["MidMarket"][plan]["gmv"] * seasonal_multiplier
            base_fee = subscription_fee["MidMarket"][plan]
            revenue = base_fee + gmv * commission_rate

            customers[cid] = {"active": True, "segment": "MidMarket", "plan": plan}
            month_log.append({"Month": month, "Customer_ID": cid, "Revenue": revenue})
            monthly_revenue += revenue
            customer_id_counter += 1

        journey_log.extend(month_log)

    df = pd.DataFrame(journey_log)

    revenue_matrix = df.pivot_table(
        index="Customer_ID",
        columns="Month",
        values="Revenue",
        aggfunc="sum",
        fill_value=0,
    ).reset_index()

    summary_df = (
        df.groupby("Month")
        .agg(
            Total_Customers=("Customer_ID", "nunique"), Total_Revenue=("Revenue", "sum")
        )
        .reset_index()
    )

    summary_df["Target_Revenue"] = monthly_revenue_targets
    summary_df["Revenue_Gap"] = (
        summary_df["Total_Revenue"] - summary_df["Target_Revenue"]
    )
    summary_df["Target_Customers"] = target_end_counts
    summary_df["Customer_Gap"] = (
        summary_df["Total_Customers"] - summary_df["Target_Customers"]
    )

    return revenue_matrix, summary_df


months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
]

plans = {
    "PLG": {
        "starter": {"percentage": 0.0, "gmv": 100},
        "pro": {"percentage": 0.0, "gmv": 200},
    },
    "MidMarket": {
        "team": {"percentage": 0.6, "gmv": 500},
        "enterprise": {"percentage": 0.4, "gmv": 1000},
    },
}

subscription_fee = {
    "PLG": {"starter": 10, "pro": 25},
    "MidMarket": {"team": 100, "enterprise": 200},
}

monthly_revenue_targets = [
    82600,
    91800,
    104100,
    119700,
    134700,
    151300,
    169600,
    181800,
    199100,
    217300,
    241800,
    262100,
]

revenue_matrix, summary_df = simulate_customer_revenue_matrix(
    start_customers=954,
    end_customers=372,
    months=months,
    monthly_revenue_targets=monthly_revenue_targets,
    plg_ratio_start=0.9,
    plg_ratio_end=0.22,
    plans=plans,
    subscription_fee=subscription_fee,
    commission_rate=0.1,
)

revenue_matrix.to_csv("customer_revenue_matrix.csv", index=False)
summary_df.to_csv("monthly_summary.csv", index=False)

print("✅ Files saved: 'customer_revenue_matrix.csv' and 'monthly_summary.csv'")
