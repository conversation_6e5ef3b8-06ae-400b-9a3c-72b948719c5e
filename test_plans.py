# Final Simulation Script for Revenue Target Matching with Margin Weighting and Slack Constraints

import numpy as np
import pandas as pd
from scipy.optimize import linprog

# Constants
months = [
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
    "Jan",
    "Feb",
    "Mar",
]

# Plans and Pricing
gmv_base = {
    "PLG": {"starter": 100, "pro": 200},
    "MidMarket": {"team": 500, "enterprise": 1000},
}

subscription_fee = {
    "PLG": {"starter": 10, "pro": 25},
    "MidMarket": {"team": 100, "enterprise": 200},
}

commission_rate = 0.1

# Seasonal GMV multipliers per month (e.g. Black Friday in Nov)
gmv_multipliers = [1.0, 1.05, 1.1, 1.15, 1.2, 1.25, 1.3, 1.5, 1.6, 1.3, 1.2, 1.1]

# Plan ordering
plan_keys = ["PLG_starter", "PLG_pro", "MidMarket_team", "MidMarket_enterprise"]

# Plan margins (used to bias LP objective)
plan_margins = np.array([10, 25, 150, 300])

# Targets
start_customers = 954
end_customers = 372
revenue_targets = [
    82600,
    91800,
    104100,
    119700,
    134700,
    151300,
    169600,
    181800,
    199100,
    217300,
    241800,
    262100,
]
plg_mid_mix = [0.8, 0.75, 0.7, 0.7, 0.65, 0.6, 0.5, 0.45, 0.4, 0.35, 0.3, 0.25]


# Helper to compute revenue per plan for a given month
def get_monthly_revenue_vector(month_idx):
    seasonal_multiplier = gmv_multipliers[month_idx]
    revenue_per_plan = []
    for key in plan_keys:
        category, tier = key.split("_")
        sub_fee = subscription_fee[category][tier]
        gmv = gmv_base[category][tier] * seasonal_multiplier
        commission = gmv * commission_rate if category == "MidMarket" else 0
        revenue = sub_fee + commission
        revenue_per_plan.append(revenue)
    return np.array(revenue_per_plan)


# Run simulation
def simulate_customer_revenue_matrix():
    monthly_allocations = []
    summary_data = []

    customer_counts = np.linspace(start_customers, end_customers, len(months)).astype(
        int
    )

    for i, month in enumerate(months):
        revenue_target = revenue_targets[i]
        customers = customer_counts[i]
        plg_ratio = plg_mid_mix[i]

        revenue_per_plan = get_monthly_revenue_vector(i)
        margin_weight = -plan_margins  # Negative for maximization

        A_eq = [revenue_per_plan, [1] * 4]
        b_eq = [revenue_target, customers]

        bounds = [(0, customers) for _ in range(4)]

        res = linprog(
            margin_weight,
            A_eq=A_eq,
            b_eq=b_eq,
            bounds=bounds,
            method="highs",
        )

        if res.success:
            plan_counts = np.round(res.x).astype(int)
            revenue = np.dot(plan_counts, revenue_per_plan)
        else:
            plan_counts = [int(customers * ratio) for ratio in [0.25, 0.25, 0.25, 0.25]]
            revenue = np.dot(plan_counts, revenue_per_plan)

        allocation = {
            "Month": month,
            **{plan_keys[j]: plan_counts[j] for j in range(4)},
        }
        summary = {
            "Month": month,
            "Total_Customers": sum(plan_counts),
            "Total_Revenue": round(revenue),
            "Target_Revenue": revenue_target,
            "Revenue_Gap": round(revenue - revenue_target),
            "Target_Customers": customers,
            "Customer_Gap": sum(plan_counts) - customers,
        }

        monthly_allocations.append(allocation)
        summary_data.append(summary)

    allocation_df = pd.DataFrame(monthly_allocations)
    summary_df = pd.DataFrame(summary_data)

    allocation_df.to_csv("customer_plan_allocation.csv", index=False)
    summary_df.to_csv("summary_report.csv", index=False)
    print("✅ Files saved: customer_plan_allocation.csv and summary_report.csv")


if __name__ == "__main__":
    simulate_customer_revenue_matrix()
